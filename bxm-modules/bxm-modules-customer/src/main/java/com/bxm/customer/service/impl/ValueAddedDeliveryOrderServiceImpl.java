package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.customer.mapper.ValueAddedDeliveryOrderMapper;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.ValueAddedValidationService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 增值交付单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedDeliveryOrderServiceImpl extends ServiceImpl<ValueAddedDeliveryOrderMapper, ValueAddedDeliveryOrder> implements IValueAddedDeliveryOrderService
{

    @Autowired
    private ValueAddedValidationService validationService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueAddedDeliveryOrder upsert(ValueAddedDeliveryOrderVO orderVO) {
        try {
            log.info("Starting upsert operation for delivery order: {}", orderVO.getCustomerName());

            // 执行基础校验
            validateOrderVO(orderVO);

            // VO转换为DO
            ValueAddedDeliveryOrder order = new ValueAddedDeliveryOrder();
            BeanUtils.copyProperties(orderVO, order);

            // 根据增值事项名称执行特殊校验和处理逻辑
            validationService.validateByItemName(orderVO, order);

            // 查找现有记录
            ValueAddedDeliveryOrder existingOrder = findExistingOrder(order);

            if (existingOrder != null) {
                // 更新现有记录
                log.info("Updating existing delivery order: {}", existingOrder.getDeliveryOrderNo());
                updateExistingOrder(existingOrder, order);
                updateById(existingOrder);
                return existingOrder;
            } else {
                // 创建新记录
                // 设置默认状态
                if (StringUtils.isEmpty(order.getStatus())) {
                    order.setStatus(ValueAddedDeliveryOrderStatus.getDefaultStatus().getCode()); // 使用枚举设置默认状态
                }
                // 设置默认删除标志
                order.setIsDel(false);
                // 设置创建人信息
                try {
                    Long userId = SecurityUtils.getUserId();
                    order.setCreateUid(userId); // set creator uid
                    SysUser sysUser = remoteUserService.getByUserId(userId, SecurityConstants.INNER).getDataThrowException();
                    order.setCreateBy(Objects.isNull(sysUser) ? "" : sysUser.getNickName()); // set creator nick name to createBy
                } catch (Exception ex) {
                    log.warn("Failed to set create user info for delivery order, reason: {}", ex.getMessage());
                }
                save(order);
                log.info("Created new delivery order: {}", order.getDeliveryOrderNo());
                return order;
            }

        } catch (Exception e) {
            log.error("Failed to upsert delivery order for customer: {}", orderVO.getCustomerName(), e);
            throw new RuntimeException("保存增值交付单失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValueAddedDeliveryOrder getByDeliveryOrderNo(String deliveryOrderNo) {
        if (StringUtils.isEmpty(deliveryOrderNo)) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNo.trim())
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    @Override
    public ValueAddedDeliveryOrder getByCustomerIdAndItemType(Long customerId, Integer valueAddedItemType) {
        if (customerId == null || valueAddedItemType == null) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getCustomerId, customerId)
                .eq(ValueAddedDeliveryOrder::getValueAddedItemTypeId, valueAddedItemType)
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    /**
     * 验证订单VO
     */
    private void validateOrderVO(ValueAddedDeliveryOrderVO orderVO) {
        // 验证交付单编号（必填字段）
        if (StringUtils.isEmpty(orderVO.getDeliveryOrderNo())) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 验证交付单编号格式（VAD开头，总长度19位）
        // 格式：VAD + yyMMddHHmmsss(13位数字) + 3位随机码(数字+大写字母)
        if (!orderVO.getDeliveryOrderNo().matches("^VAD\\d{13}[0-9A-Z]{3}$")) {
            throw new IllegalArgumentException("交付单编号格式不正确，应为VAD+时间戳到毫秒+3位随机码，总长度19位");
        }


        // 验证状态（如果提供了状态）
        if (StringUtils.isNotEmpty(orderVO.getStatus()) && !ValueAddedDeliveryOrderStatus.isValid(orderVO.getStatus())) {
            throw new IllegalArgumentException("无效的交付状态: " + orderVO.getStatus());
        }

        // 验证纳税性质
        if (orderVO.getTaxpayerType() != null &&
            (orderVO.getTaxpayerType() < 1 || orderVO.getTaxpayerType() > 2)) {
            throw new IllegalArgumentException("无效的纳税性质: " + orderVO.getTaxpayerType());
        }

        // 验证账期逻辑（账期格式为YYYYMM的整数，如：202301）
        if (orderVO.getAccountingPeriodStart() != null && orderVO.getAccountingPeriodEnd() != null) {
            // 使用数值比较验证账期开始时间不能晚于结束时间
            if (orderVO.getAccountingPeriodStart() > orderVO.getAccountingPeriodEnd()) {
                throw new IllegalArgumentException("账期开始时间不能晚于结束时间");
            }
        }
    }

    /**
     * 查找现有记录
     */
    private ValueAddedDeliveryOrder findExistingOrder(ValueAddedDeliveryOrder order) {

        if (StringUtils.isNotEmpty(order.getDeliveryOrderNo())) {
            return getByDeliveryOrderNo(order.getDeliveryOrderNo());
        }
        return null;
    }

    /**
     * 更新现有记录
     */
    private void updateExistingOrder(ValueAddedDeliveryOrder existingOrder, ValueAddedDeliveryOrder newOrder) {
        // 保留原有的ID、交付单编号和创建相关信息
        Long originalId = existingOrder.getId();
        String originalDeliveryOrderNo = existingOrder.getDeliveryOrderNo();
        String originalCreateBy = existingOrder.getCreateBy();
        Long originalCreateUid = existingOrder.getCreateUid();
        // 使用BeanUtils复制所有属性
        BeanUtils.copyProperties(newOrder, existingOrder);
        // 恢复不应该被更新的字段
        existingOrder.setId(originalId);
        existingOrder.setDeliveryOrderNo(originalDeliveryOrderNo);
        existingOrder.setCreateBy(originalCreateBy);
        existingOrder.setCreateUid(originalCreateUid);
    }


}
