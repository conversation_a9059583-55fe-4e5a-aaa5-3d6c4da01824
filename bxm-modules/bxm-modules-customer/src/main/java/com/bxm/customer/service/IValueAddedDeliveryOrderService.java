package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.customer.domain.query.DeliveryOrderQuery;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 增值交付单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IValueAddedDeliveryOrderService extends IService<ValueAddedDeliveryOrder>
{

    /**
     * 新增或更新增值交付单
     *
     * 支持以下场景：
     * 1. 新增：不提供ID和交付单编号，系统自动生成交付单编号
     * 2. 更新：提供ID或交付单编号，更新现有记录
     * 3. 智能判断：根据客户信息和增值事项判断是否存在重复记录
     *
     * @param orderVO 增值交付单VO对象，包含完整的验证注解和业务字段
     * @return 操作结果，包含交付单编号和操作类型信息
     * @throws IllegalArgumentException 当参数验证失败时抛出（如必填字段为空、格式不正确等）
     * @throws RuntimeException 当业务处理失败时抛出（如数据库操作失败等）
     */
    ValueAddedDeliveryOrder upsert(@Valid @NotNull ValueAddedDeliveryOrderVO orderVO);

    /**
     * 根据交付单编号查询增值交付单
     *
     * @param deliveryOrderNo 交付单编号
     * @return 增值交付单对象，如果不存在则返回null
     */
    ValueAddedDeliveryOrder getByDeliveryOrderNo(String deliveryOrderNo);

    /**
     * 根据客户ID和增值事项查询增值交付单
     *
     * @param customerId 客户ID
     * @param valueAddedItemType 增值事项类型
     * @return 增值交付单对象，如果不存在则返回null
     */
    ValueAddedDeliveryOrder getByCustomerIdAndItemType(Long customerId, Integer valueAddedItemType);

    /**
     * 增值交付单条件查询
     *
     * 所有条件在 Service 中动态拼接
     *
     * @param query 查询参数
     * @return 结果列表
     */
    List<ValueAddedDeliveryOrder> query(DeliveryOrderQuery query);
}
